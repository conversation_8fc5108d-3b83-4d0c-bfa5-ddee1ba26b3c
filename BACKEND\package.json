{"name": "backend", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.2", "morgan": "^1.10.1", "nanoid": "^5.1.5", "nodemon": "^3.1.10"}}