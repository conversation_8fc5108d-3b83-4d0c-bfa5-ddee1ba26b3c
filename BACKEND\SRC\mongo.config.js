import moongoose from "mongoose";
console.log(process.env.MONGO_URI);
const connectDB = async () => { 
    try { 
        const conn = await moongoose.connect(process.env.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log(`MongoDB Connected: ${conn.connection.host}`);
    } catch (error) { 
        console.error(`Error: ${error.message}`);
        process.exit(1);
    }
};
export default connectDB;